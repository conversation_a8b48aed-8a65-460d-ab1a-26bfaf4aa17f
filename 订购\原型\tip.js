// 根据新的接口返回结构处理数据
let v = [];

if ($state.form.billingType === "1") {
  // 循环处理所有items
  $state.priceResp.items.forEach(item => {
    // 构建资源包类型和规格的详细信息
    const resourceType = '流量包';

    // 构建配置详情字符串，按照图片中的格式
    const resourceName = `${$state.resourceTypeOption.find(itm => itm.value === item.resourceType)?.label || '流量包'}`
    let configDetail = [
      `资源包类型：${resourceName}`,
      `资源包规格：${item.resourceUint}`,
      `加速区域：${item.areaName}`
    ];

    // 如果有计费区域信息且不是中国内地，添加计费区域
    if (item.billingArea !== 0 && item.areaName !== '中国内地' && resourceName === '流量包') {
      const label = `${$state.overseaRegionOptions.find(itm => itm.value === item.overseaRegion)?.label}`
      if (label) configDetail.push(`计费区域：${label}`);

    }

    // 添加购买时长
    configDetail.push(`购买时长：${item.peroid}年`);

    // 将配置详情数组转换为字符串，每项用换行符分隔
    const detailString = configDetail.join(' | ');

    // 添加到数据项
    v.push({
      name: "全站加速资源包",
      detail: detailString,
      count: `${item.purchaseNum}`,
      price: Number(item.finalPrice)
    });
  });
} else {
  // 按量计费逻辑 - 处理费用明细的格式化显示

  // 从 asd.js 导入 saleList 配置信息
  const saleList = [
    { "label": "全站加速-日带宽峰值计费-中国内地-2022", "value": "ec022991c7924d25af8e00e9aeb87a58" },
    { "label": "全站加速-流量计费-2022", "value": "a10029274de34c3880067c4f9a760ae1" },
    { "label": "上传加速-日带宽峰值计费", "value": "467877caf02f41b68aecd6665e409e0e" },
    { "label": "上传加速-流量计费", "value": "9785a0a6230447a0a6f3b674529cf453" },
    { "label": "websocket加速-日带宽峰值计费", "value": "fab645fc2a16483e99cdb7d907cc44d8" },
    { "label": "websocket加速-流量计费", "value": "9eaed8d377ae45b3b2c02bc403330a46" },
    { "label": "全站加速-动态请求数计费", "value": "8b809ccdac024f86b3cb54646fcf6884" },
    { "label": "全站加速-静态https请求数计费", "value": "4d1f7565d5bc47478d402a465bbfc9d3" },
    { "label": "全站加速-日带宽峰值计费-全球（不含中国内地）-北美", "value": "c5e83d631d144d2a8f68b37c10a8c720" },
    { "label": "全站加速-日带宽峰值计费-全球（不含中国内地）-欧洲", "value": "a83e392970ae42dfa4bd1f1e01acd7a2" },
    { "label": "全站加速-日带宽峰值计费-全球（不含中国内地）-亚太1区", "value": "9419fd0a096a469f9bed8a220883c503" },
    { "label": "全站加速-日带宽峰值计费-全球（不含中国内地）-亚太2区", "value": "959ae6babd0c45b4b071f80b4ffe9be1" },
    { "label": "全站加速-日带宽峰值计费-全球（不含中国内地）-亚太3区", "value": "f59f6267431d41b7b3f8926ffe930331" },
    { "label": "全站加速-日带宽峰值计费-全球（不含中国内地）-中东非", "value": "135230be2bb1432ba28a5878439e9b81" },
    { "label": "全站加速-日带宽峰值计费-全球（不含中国内地）-南美", "value": "92ee7d7b57f443acae463a95e81a88da" },
    { "label": "全站加速-流量计费-全球（不含中国内地）-北美", "value": "89b7380c6c574068a24a0933587472da" },
    { "label": "全站加速-流量计费-全球（不含中国内地）-欧洲", "value": "30d2196ed08c44ad8a2db676a712954c" },
    { "label": "全站加速-流量计费-全球（不含中国内地）-亚太1区", "value": "40c380c9092e4a69b0ea02e4f74ea5ef" },
    { "label": "全站加速-流量计费-全球（不含中国内地）-亚太2区", "value": "480b153d26cd4095bd7b1c15f619cf05" },
    { "label": "全站加速-流量计费-全球（不含中国内地）-亚太3区", "value": "632ccfab50344cd5ada2474a2aec577e" },
    { "label": "全站加速-流量计费-全球（不含中国内地）-中东非", "value": "087a357679ca454a85afd757f9f37fe7" },
    { "label": "全站加速-流量计费-全球（不含中国内地）-南美", "value": "f25927af761c4a99abe2867c95ce29c3" },
    { "label": "全站加速-动态请求数计费-全球（不含中国内地）", "value": "835a771e26f348cd800909ad56ff7a66" },
    { "label": "全站加速-静态https请求数计费-全球（不含中国内地）", "value": "ed6e8403b11a498bb3c7d7cb866f371e" }
  ];

  // 辅助函数：根据 saleId 查找对应的配置信息
  const findSaleConfig = (saleId) => {
    return saleList.find(item => item.value === saleId);
  };

  // 辅助函数：解析计费项类型
  const parseServiceType = (label) => {
    if (label.includes('上传加速')) return '上传加速流量包';
    if (label.includes('websocket加速')) return 'websocket加速流量包';
    return '全站加速';
  };

  // 辅助函数：解析资源包类型
  const parseResourceType = (label) => {
    if (label.includes('上传加速')) return '上传加速流量包';
    if (label.includes('websocket加速')) return 'websocket加速流量包';
    if (label.includes('动态请求数计费')) return '动态请求数';
    if (label.includes('静态https请求数计费')) return '静态HTTPS请求数';
    return '流量包';
  };

  // 辅助函数：解析计费方式
  const parseBillingMethod = (label) => {
    if (label.includes('日带宽峰值计费')) return '日带宽峰值';
    if (label.includes('流量计费')) return '流量';
    if (label.includes('动态请求数计费')) return '动态请求数';
    if (label.includes('静态https请求数计费')) return '静态HTTPS请求数';
    return '';
  };

  // 辅助函数：解析加速区域
  const parseRegion = (label) => {
    if (label.includes('中国内地')) return '中国内地';
    if (label.includes('全球（不含中国内地）')) return '全球（不含中国内地）';
    return '';
  };

  // 辅助函数：解析具体区域（针对全球区域）
  const parseSpecificRegion = (label) => {
    if (label.includes('-北美')) return '北美';
    if (label.includes('-欧洲')) return '欧洲';
    if (label.includes('-亚太1区')) return '亚太1区';
    if (label.includes('-亚太2区')) return '亚太2区';
    if (label.includes('-亚太3区')) return '亚太3区';
    if (label.includes('-中东非')) return '中东/非洲';
    if (label.includes('-南美')) return '南美';
    return '';
  };

  // 辅助函数：格式化数值和单位
  const formatValueWithUnit = (value, unit) => {
    if (!value || value === 0) return '-';
    return `${value}${unit || ''}`;
  };

  // 辅助函数：获取购买时长
  const getPurchaseDuration = () => {
    const period = $state.form.purchasePeriod || 1;
    const unit = $state.form.purchasePeriodUnit || '年';
    return `${period}${unit}`;
  };

  // 辅助函数：构建全球区域的带宽/流量明细
  const buildGlobalRegionDetails = (items) => {
    const regionMap = {
      '北美': { value: 0, unit: '' },
      '欧洲': { value: 0, unit: '' },
      '亚太1区': { value: 0, unit: '' },
      '亚太2区': { value: 0, unit: '' },
      '亚太3区': { value: 0, unit: '' },
      '中东/非洲': { value: 0, unit: '' },
      '南美': { value: 0, unit: '' }
    };

    // 遍历所有项目，收集各区域的数据
    items.forEach(item => {
      const saleConfig = findSaleConfig(item.itemId);
      if (saleConfig && saleConfig.label.includes('全球（不含中国内地）')) {
        const specificRegion = parseSpecificRegion(saleConfig.label);
        if (specificRegion && regionMap[specificRegion] !== undefined) {
          regionMap[specificRegion] = {
            value: item.value || item.amount || 0,
            unit: item.unit || ''
          };
        }
      }
    });

    // 构建区域明细字符串
    const regionDetails = [];
    Object.keys(regionMap).forEach(region => {
      const data = regionMap[region];
      if (data.value > 0) {
        regionDetails.push(`${region}：${formatValueWithUnit(data.value, data.unit)}`);
      }
    });

    return regionDetails.length > 0 ? regionDetails.join('，') : '';
  };

  // 遍历接口返回的 items 数组
  if ($state.data && $state.data.items && Array.isArray($state.data.items)) {
    // 按服务类型和计费方式分组处理
    const processedGroups = new Set();

    $state.data.items.forEach(item => {
      // 根据 itemId 查找对应的配置信息
      const saleConfig = findSaleConfig(item.itemId);

      if (saleConfig) {
        const serviceType = parseServiceType(saleConfig.label);
        const billingMethod = parseBillingMethod(saleConfig.label);
        const region = parseRegion(saleConfig.label);

        // 创建分组键
        const groupKey = `${serviceType}-${billingMethod}-${region}`;

        // 如果是全球区域的带宽/流量计费，需要特殊处理
        if (region === '全球（不含中国内地）' &&
            (billingMethod === '日带宽峰值' || billingMethod === '流量') &&
            !processedGroups.has(groupKey)) {

          processedGroups.add(groupKey);

          // 构建配置详情字符串
          let configDetails = [];

          // 添加资源包类型
          const resourceType = parseResourceType(saleConfig.label);
          configDetails.push(`资源包类型：${resourceType}`);

          // 添加资源包规格（按量计费显示为 -）
          configDetails.push('资源包规格：-');

          // 添加加速区域
          configDetails.push(`加速区域：${region}`);

          // 构建全球区域的详细信息
          const globalRegionDetails = buildGlobalRegionDetails($state.data.items);
          if (globalRegionDetails) {
            configDetails.push(`计费区域：${globalRegionDetails}`);
          }

          // 添加购买时长
          configDetails.push(`购买时长：${getPurchaseDuration()}`);

          // 添加购买量（按量计费默认显示 -）
          configDetails.push('购买量：-');

          // 构建最终的详情字符串
          const detail = configDetails.join(' | ');

          // 计算该组的总价格
          let totalPrice = 0;
          $state.data.items.forEach(groupItem => {
            const groupSaleConfig = findSaleConfig(groupItem.itemId);
            if (groupSaleConfig &&
                parseServiceType(groupSaleConfig.label) === serviceType &&
                parseBillingMethod(groupSaleConfig.label) === billingMethod &&
                parseRegion(groupSaleConfig.label) === region) {
              totalPrice += Number(groupItem.finalPrice || groupItem.totalPrice || 0);
            }
          });

          // 添加到结果数组
          v.push({
            name: "全站加速资源包",
            detail: detail,
            count: getPurchaseDuration(),
            price: totalPrice
          });

        } else if (region === '中国内地' && !processedGroups.has(groupKey)) {
          // 处理中国内地区域
          processedGroups.add(groupKey);

          // 构建配置详情字符串
          let configDetails = [];

          // 添加资源包类型
          const resourceType = parseResourceType(saleConfig.label);
          configDetails.push(`资源包类型：${resourceType}`);

          // 添加资源包规格（按量计费显示为 -）
          configDetails.push('资源包规格：-');

          // 添加加速区域
          if (region) {
            configDetails.push(`加速区域：${region}`);
          }

          // 添加购买时长
          configDetails.push(`购买时长：${getPurchaseDuration()}`);

          // 添加购买量（按量计费默认显示 -）
          configDetails.push('购买量：-');

          // 构建最终的详情字符串
          const detail = configDetails.join(' | ');

          // 添加到结果数组
          v.push({
            name: "全站加速资源包",
            detail: detail,
            count: getPurchaseDuration(),
            price: Number(item.finalPrice || item.totalPrice || 0)
          });
        }
      }
    });
  }

  // 如果没有找到任何配置项，添加默认项
  if (v.length === 0) {
    v.push({
      name: "全站加速资源包",
      detail: "暂无配置详情",
      count: getPurchaseDuration(),
      price: 0
    });
  }

}


// 调用显示详情的方法
$refs['fa78f38f'].showDetail(v);
